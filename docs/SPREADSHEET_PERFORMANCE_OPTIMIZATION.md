# Оптимизация производительности таблиц Spreadsheet

## Проблема

В разделе "Расчёты" наблюдались сильные фризы анимации Sidebar при сворачивании/разворачивании. Проблема была связана с тем, что таблицы Spreadsheet и SpreadsheetReact используют автоматическое определение ширины, что вызывает постоянные пересчеты во время анимации.

## Причины проблемы

1. **ResizeObserver без дебаунсинга** - срабатывает при каждом изменении размера во время анимации
2. **Автоматическое определение ширины таблиц** - вызывает пересчеты layout во время анимации
3. **Отсутствие оптимизации CSS анимации** - не используется аппаратное ускорение
4. **Синхронные обновления таблиц** - блокируют основной поток во время анимации

## Реализованные решения

### 1. Дебаунсированный ResizeObserver

**Файл:** `src/pages/CalculationsPage/ui/StationBody/lib/helpers.ts`

Добавлена функция `debouncedResize` с задержкой 150-200мс для предотвращения частых обновлений:

```typescript
export const debouncedResize = (
  refUp: RefObject<HTMLDivElement>,
  setHeightUp: Dispatch<SetStateAction<number | undefined>>,
  setWidthUp: Dispatch<SetStateAction<number | undefined>>,
  delay: number = 150
) => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
  
  resizeTimeout = setTimeout(() => {
    resize(refUp, setHeightUp, setWidthUp)
  }, delay)
}
```

### 2. Оптимизированный хук useOptimizedResizeObserver

**Файл:** `src/shared/hooks/useOptimizedResizeObserver.ts`

Создан специальный хук с возможностями:
- Дебаунсинг обновлений
- Пауза во время анимации Sidebar
- Минимальный порог изменения размера
- Автоматическое обновление после окончания анимации

```typescript
const containerRef = useOptimizedResizeObserver(
  ({ width }) => {
    setContainerWidth(Math.max(width, 800))
  },
  {
    debounceDelay: 200,
    pauseDuringAnimation: true,
    threshold: 10
  }
)
```

### 3. Менеджер производительности SpreadsheetPerformanceManager

**Файл:** `src/shared/lib/spreadsheetPerformance/index.ts`

Глобальный менеджер, который:
- Отслеживает анимацию Sidebar
- Временно отключает анимации таблиц во время анимации Sidebar
- Принудительно обновляет таблицы после окончания анимации
- Добавляет CSS оптимизации

### 4. Оптимизация CSS анимации Sidebar

**Файл:** `src/widgets/SideBar/SideBar.module.scss`

Добавлены оптимизации:
- Использование `cubic-bezier` для плавной анимации
- `will-change: width` для подготовки браузера к анимации
- `transform: translateZ(0)` для включения аппаратного ускорения

```scss
.SideBar {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s ease;
  will-change: width;
  transform: translateZ(0);
}
```

### 5. Динамическая ширина таблиц

**Файл:** `src/pages/CalculationsPage/ui/VaultBody/ui/AVRCHMSpreadsheet/AVRCHMSpreadsheet.tsx`

Заменена фиксированная ширина `'1000px'` на динамическую с оптимизированным ResizeObserver:

```typescript
const [containerWidth, setContainerWidth] = useState(1000)
const containerRef = useOptimizedResizeObserver(...)

// В JSX:
<SpreadsheetReact width={`${containerWidth}px`} ... />
```

## Использование

### Автоматическая инициализация

Менеджер производительности автоматически инициализируется в `src/app/App.tsx`:

```typescript
useEffect(() => {
  const performanceManager = initSpreadsheetPerformance({
    debounceDelay: 200,
    pauseDuringAnimation: true,
    threshold: 10
  })

  return () => {
    performanceManager?.destroy()
  }
}, [])
```

### Использование в компонентах таблиц

1. **Для новых компонентов:**
```typescript
import { useOptimizedResizeObserver } from 'shared/hooks'

const containerRef = useOptimizedResizeObserver(
  ({ width }) => setTableWidth(width),
  { debounceDelay: 200, pauseDuringAnimation: true }
)

return (
  <div ref={containerRef}>
    <SpreadsheetReact width={`${tableWidth}px`} ... />
  </div>
)
```

2. **Для существующих компонентов:**
Заменить `resize` на `debouncedResize` в ResizeObserver:

```typescript
const resizeObserver = new ResizeObserver(() => {
  debouncedResize(refUp, () => {}, setWidthUp, 200)
})
```

## Результат

После применения всех оптимизаций:
- ✅ Анимация Sidebar стала плавной
- ✅ Таблицы корректно адаптируются к изменению размера
- ✅ Нет фризов во время анимации
- ✅ Сохранена функциональность динамического определения ширины

## Дополнительные рекомендации

1. **Мониторинг производительности:** Используйте DevTools для отслеживания производительности
2. **Тестирование:** Проверьте работу на разных устройствах и браузерах
3. **Расширение:** При добавлении новых таблиц используйте `useOptimizedResizeObserver`
4. **Настройка:** Параметры дебаунсинга можно настроить в зависимости от требований

## Файлы, затронутые изменениями

- `src/pages/CalculationsPage/ui/StationBody/lib/helpers.ts` - дебаунсированный resize
- `src/pages/CalculationsPage/ui/StationBody/ui/StationBody.tsx` - использование debouncedResize
- `src/pages/CalculationsPage/ui/VaultBody/ui/AVRCHMSpreadsheet/AVRCHMSpreadsheet.tsx` - оптимизированный ResizeObserver
- `src/shared/hooks/useOptimizedResizeObserver.ts` - новый хук
- `src/shared/lib/spreadsheetPerformance/index.ts` - менеджер производительности
- `src/widgets/SideBar/SideBar.module.scss` - CSS оптимизации
- `src/app/App.tsx` - инициализация менеджера производительности
