{"name": "neptune-web", "private": true, "version": "2.3.1", "type": "module", "scripts": {"start": "vite", "start:host": "vite --host", "build": "tsc && vite build", "preview:build": "tsc && vite build & vite preview", "preview": "tsc && vite preview", "lint:ts": "eslint \"**/*.{ts,tsx}\"", "lint:ts:fix": "eslint --fix", "postinstall": "patch-package", "unit": "vitest run", "unit:coverage": "vitest run --coverage.enabled --coverage.reporter=text", "lint:vite": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:vite:fix": "eslint --fix", "lint": "eslint \"**/*.{ts,tsx}\" --fix", "ts:check": "tsc --noEmit", "test": "vitest", "husky:install": "husky init", "prepare": "husky", "lint:prettier": "prettier --write --ignore-unknown **/*", "commit": "node commit.js", "prepush": "npm run ts:check"}, "devDependencies": {"@babel/core": "7.22.5", "@babel/preset-env": "7.22.5", "@babel/preset-react": "7.22.5", "@babel/preset-typescript": "7.22.5", "@ic-ntcees/eslint-config-react": "1.0.0", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "14.0.0", "@types/js-cookie": "3.0.3", "@types/node": "20.3.1", "@types/qs": "6.9.15", "@types/react": "18.2.56", "@types/react-dom": "18.2.19", "@types/react-router-dom": "5.3.3", "@types/react-window": "1.8.5", "@types/sockjs-client": "1.5.1", "@types/uuid": "9.0.2", "@vitejs/plugin-react": "4.3.1", "@vitejs/plugin-react-swc": "3.5.0", "@vitest/coverage-v8": "1.6.0", "babel-loader": "9.1.2", "babel-plugin-react-generate-property": "1.1.2", "css-loader": "6.8.1", "file-loader": "6.2.0", "globals": "15.12.0", "husky": "9.0.11", "identity-obj-proxy": "3.0.0", "js-file-download": "0.4.12", "jsdom": "24.0.0", "prettier": "3.4.1", "react-refresh": "0.14.0", "regenerator-runtime": "0.13.11", "sass": "1.63.6", "sass-loader": "13.3.2", "style-loader": "3.3.3", "stylelint": "15.8.0", "stylelint-config-standard-scss": "9.0.0", "ts-loader": "9.4.3", "ts-node": "10.9.2", "typescript": "5.5.3", "vite": "5.4.18", "vite-plugin-svgr": "4.2.0", "vitest": "1.6.0"}, "dependencies": {"@devexpress/dx-react-core": "4.0.4", "@devexpress/dx-react-grid": "4.0.4", "@devexpress/dx-react-grid-material-ui": "4.0.4", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@handsontable/react": "15.3.0", "@mui/icons-material": "5.14.6", "@mui/material": "5.13.6", "@mui/x-date-pickers": "6.9.1", "@mui/x-date-pickers-pro": "6.9.1", "@stomp/stompjs": "7.0.0", "@types/inquirer": "9.0.7", "axios": "1.8.2", "date-fns": "2.30.0", "dotenv": "16.4.5", "handsontable": "15.3.0", "highcharts": "11.4.1", "highcharts-react-official": "3.2.0", "hyperformula": "3.0.0", "inquirer": "10.0.1", "js-cookie": "3.0.5", "mobx": "6.9.0", "mobx-react": "7.6.0", "patch-package": "8.0.0", "qs": "6.12.1", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-hotkeys-hook": "4.4.1", "react-router-dom": "6.20.0", "socket.io-client": "4.7.1", "sockjs-client": "1.6.1", "uuid": "9.0.0"}}