/**
 * Утилиты для оптимизации производительности таблиц Spreadsheet
 * во время анимации Sidebar и других UI элементов
 */

interface SpreadsheetPerformanceOptions {
  /** Селектор для элемента Sidebar (по умолчанию '[data-testid="sidebar"]') */
  sidebarSelector?: string
  /** Класс для временного отключения анимаций таблиц */
  disableAnimationsClass?: string
  /** Задержка перед восстановлением анимаций после окончания анимации Sidebar */
  restoreDelay?: number
}

class SpreadsheetPerformanceManager {
  readonly options: Required<SpreadsheetPerformanceOptions>
  private isAnimating = false
  private restoreTimeout: NodeJS.Timeout | null = null

  constructor(options: SpreadsheetPerformanceOptions = {}) {
    this.options = {
      sidebarSelector: '[data-testid="sidebar"]',
      disableAnimationsClass: 'spreadsheet-animations-disabled',
      restoreDelay: 100,
      ...options,
    }

    this.init()
  }

  private init() {
    this.addStyles()
    this.setupEventListeners()
  }

  private addStyles() {
    const styleId = 'spreadsheet-performance-styles'
    if (document.getElementById(styleId)) return

    const style = document.createElement('style')
    style.id = styleId
    style.textContent = `
      .${this.options.disableAnimationsClass} .handsontable,
      .${this.options.disableAnimationsClass} .ht_master,
      .${this.options.disableAnimationsClass} .wtHolder {
        transition: none !important;
        animation: none !important;
      }
      
      .${this.options.disableAnimationsClass} .handsontable .wtHider {
        transition: none !important;
      }
      
      /* Оптимизация рендеринга таблиц */
      .handsontable {
        contain: layout style paint;
      }
      
      .handsontable .wtHolder {
        will-change: auto;
      }
      
      /* Во время анимации Sidebar отключаем will-change для таблиц */
      .${this.options.disableAnimationsClass} .handsontable .wtHolder {
        will-change: auto !important;
      }
    `
    document.head.appendChild(style)
  }

  private setupEventListeners() {
    const sidebar = document.querySelector(this.options.sidebarSelector)
    if (!sidebar) return

    const handleTransitionStart = () => {
      this.disableSpreadsheetAnimations()
    }

    const handleTransitionEnd = () => {
      this.enableSpreadsheetAnimations()
    }

    sidebar.addEventListener('transitionstart', handleTransitionStart)
    sidebar.addEventListener('transitionend', handleTransitionEnd)

    // Очистка при уничтожении
    return () => {
      sidebar.removeEventListener('transitionstart', handleTransitionStart)
      sidebar.removeEventListener('transitionend', handleTransitionEnd)
    }
  }

  private disableSpreadsheetAnimations() {
    if (this.isAnimating) return

    this.isAnimating = true
    document.body.classList.add(this.options.disableAnimationsClass)

    // Принудительно обновляем все таблицы для применения стилей
    this.forceSpreadsheetUpdate()
  }

  private enableSpreadsheetAnimations() {
    if (!this.isAnimating) return

    if (this.restoreTimeout) {
      clearTimeout(this.restoreTimeout)
    }

    this.restoreTimeout = setTimeout(() => {
      this.isAnimating = false
      document.body.classList.remove(this.options.disableAnimationsClass)

      // Принудительно обновляем все таблицы для восстановления анимаций
      this.forceSpreadsheetUpdate()
    }, this.options.restoreDelay)
  }

  private forceSpreadsheetUpdate() {
    // Находим все экземпляры Handsontable и принудительно обновляем их
    const spreadsheets = document.querySelectorAll('.handsontable')
    spreadsheets.forEach((spreadsheet) => {
      const hotInstance = (spreadsheet as any).hot
      if (hotInstance && typeof hotInstance.render === 'function') {
        // Используем requestAnimationFrame для плавного обновления
        requestAnimationFrame(() => {
          try {
            hotInstance.render()
          } catch (error) {
            console.warn('Ошибка при обновлении таблицы:', error)
          }
        })
      }
    })
  }

  /**
   * Ручное отключение анимаций таблиц
   */
  public disable() {
    this.disableSpreadsheetAnimations()
  }

  /**
   * Ручное включение анимаций таблиц
   */
  public enable() {
    this.enableSpreadsheetAnimations()
  }

  /**
   * Проверка, активны ли анимации
   */
  public get isDisabled() {
    return this.isAnimating
  }

  /**
   * Уничтожение менеджера и очистка ресурсов
   */
  public destroy() {
    if (this.restoreTimeout) {
      clearTimeout(this.restoreTimeout)
    }
    document.body.classList.remove(this.options.disableAnimationsClass)

    const style = document.getElementById('spreadsheet-performance-styles')
    if (style) {
      style.remove()
    }
  }
}

// Создаем глобальный экземпляр менеджера
let globalManager: SpreadsheetPerformanceManager | null = null

/**
 * Инициализация глобального менеджера производительности таблиц
 */
export const initSpreadsheetPerformance = (options?: SpreadsheetPerformanceOptions) => {
  if (globalManager) {
    globalManager.destroy()
  }
  globalManager = new SpreadsheetPerformanceManager(options)

  return globalManager
}

/**
 * Получение глобального менеджера производительности
 */
export const getSpreadsheetPerformanceManager = () => globalManager

/**
 * Хук для использования в React компонентах
 */
export const useSpreadsheetPerformance = (options?: SpreadsheetPerformanceOptions) => {
  if (typeof window === 'undefined') return null

  if (!globalManager) {
    globalManager = new SpreadsheetPerformanceManager(options)
  }

  return globalManager
}

export { SpreadsheetPerformanceManager }
