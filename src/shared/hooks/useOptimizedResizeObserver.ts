import { useCallback, useEffect, useMemo, useRef } from 'react'

interface UseOptimizedResizeObserverOptions {
  /** Задержка дебаунсинга в миллисекундах (по умолчанию 150) */
  debounceDelay?: number
  /** Отключить observer во время анимации Sidebar (по умолчанию true) */
  pauseDuringAnimation?: boolean
  /** Минимальное изменение размера для срабатывания callback (по умолчанию 5px) */
  threshold?: number
}

interface ResizeData {
  width: number
  height: number
}

/**
 * Оптимизированный ResizeObserver хук для предотвращения фризов анимации Sidebar
 *
 * @param callback - Функция, вызываемая при изменении размера
 * @param options - Опции оптимизации
 * @returns ref для элемента, который нужно наблюдать
 */
export const useOptimizedResizeObserver = (
  callback: (data: ResizeData) => void,
  options: UseOptimizedResizeObserverOptions = {},
) => {
  const { debounceDelay = 150, pauseDuringAnimation = true, threshold = 5 } = options

  const elementRef = useRef<HTMLElement | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSizeRef = useRef<ResizeData>({ width: 0, height: 0 })
  const isAnimatingRef = useRef(false)

  // Дебаунсированный callback
  const debouncedCallback = useCallback(
    (data: ResizeData) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        // Проверяем, что изменение размера достаточно значительное
        const widthDiff = Math.abs(data.width - lastSizeRef.current.width)
        const heightDiff = Math.abs(data.height - lastSizeRef.current.height)

        if (widthDiff >= threshold || heightDiff >= threshold) {
          lastSizeRef.current = data
          callback(data)
        }
      }, debounceDelay)
    },
    [callback, debounceDelay, threshold],
  )

  // Отслеживание анимации Sidebar
  useEffect(() => {
    if (!pauseDuringAnimation) return

    const sidebar = document.querySelector('[data-testid="sidebar"]') as HTMLElement
    if (!sidebar) return

    const handleTransitionStart = () => {
      isAnimatingRef.current = true
    }

    const handleTransitionEnd = () => {
      isAnimatingRef.current = false
      // После окончания анимации принудительно обновляем размеры
      if (elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect()
        debouncedCallback({ width: rect.width, height: rect.height })
      }
    }

    sidebar.addEventListener('transitionstart', handleTransitionStart)
    sidebar.addEventListener('transitionend', handleTransitionEnd)

    return () => {
      sidebar.removeEventListener('transitionstart', handleTransitionStart)
      sidebar.removeEventListener('transitionend', handleTransitionEnd)
    }
  }, [pauseDuringAnimation, debouncedCallback])

  // ResizeObserver
  const observer = useMemo(() => {
    return new ResizeObserver((entries) => {
      // Пропускаем обновления во время анимации Sidebar
      if (pauseDuringAnimation && isAnimatingRef.current) {
        return
      }

      const entry = entries[0]
      if (entry) {
        const { width, height } = entry.contentRect
        debouncedCallback({ width, height })
      }
    })
  }, [debouncedCallback, pauseDuringAnimation])

  // Подключение observer к элементу
  useEffect(() => {
    const element = elementRef.current
    if (element) {
      observer.observe(element)

      // Инициализируем начальные размеры
      const rect = element.getBoundingClientRect()
      lastSizeRef.current = { width: rect.width, height: rect.height }
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [observer])

  return elementRef
}
