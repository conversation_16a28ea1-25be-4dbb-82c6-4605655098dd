.Select {
  width: 100%;
  font-family: var(--font-family-main) !important;

  [class*="MuiSelect"] {
    font-family: var(--font-family-main) !important;
  }

  & > div > div {
    padding: 0.15em 1em !important;
  }

  & label {
    font-size: 0.875rem !important;
    font-family: var(--font-family-main) !important;
  }

  // Стили для обрезания текста выбранных элементов
  &.truncateText {
    & > div > div {
      text-overflow: ellipsis !important;
      overflow: hidden !important;
      display: block !important;
      white-space: nowrap !important;
      padding-right: 30px !important; // Отступ до иконки стрелки (▼)
    }
  }
}

.Label {
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  display: block !important;
  white-space: nowrap !important;
  width: 90%;
}

.Menu {
  display: flex !important;
}

.Icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectInput {
  font-size: 0.875rem !important;
  font-weight: 400;

  & > div.MuiSelect-outlined {
    padding: 0.4em 1em !important;
  }

  & fieldset {
    border-radius: 4px;
  }

  & legend {
    font-family: var(--font-family-main);
  }
}

.MuiMenuItem-root {
  font-size: 0.875rem;
}
