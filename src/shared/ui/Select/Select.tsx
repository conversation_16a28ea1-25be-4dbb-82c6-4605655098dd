import { Language, PaletteOutlined } from '@mui/icons-material'
import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select as SelectMui,
  type SelectChangeEvent,
  type SelectProps as SelectPropsMui,
} from '@mui/material'
import { useTheme } from 'app/providers/ThemeProvider'
import React, { SetStateAction, useEffect, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { Icon } from 'shared/ui/Icon'
import { IconNameProps } from 'shared/ui/Icon/Icon.type.ts'

import cls from './Select.module.scss'

export interface ItemsProps {
  value?: string | number
  label?: string
  icon?: 'color' | IconNameProps | 'domain'
  color?: string
}

type SelectChangeHandler<T> = (value: T) => void

type SelectProps<T = string> = Omit<SelectPropsMui, 'defaultValue' | 'onChange'> & {
  className?: string
  label?: string
  items?: ItemsProps[]
  onChange?: SelectChangeHandler<T>
  icon?: null | string
  isOpen?: boolean
  setIsOpen?: React.Dispatch<SetStateAction<boolean>>
  onKeyDown?: React.KeyboardEventHandler<HTMLDivElement> | undefined
  focused?: boolean
  onClose?: () => void
  helperText?: string
  truncateText?: boolean // проп для обрезания текста выбранных элементов
}

const emptyItems: ItemsProps[] = []

export function Select<T = string>(props: SelectProps<T>) {
  const {
    className,
    label = '',
    variant = 'standard',
    items = emptyItems,
    onChange,
    value = '',
    id,
    setIsOpen,
    disabled = false,
    onOpen,
    onKeyDown,
    focused = false,
    error,
    helperText,
    onClose,
    multiple,
    truncateText = false,
    ...rest
  } = props
  const { theme } = useTheme()

  const handleChange = (event: SelectChangeEvent<T>) => {
    event.preventDefault()
    event.stopPropagation()
    onChange && onChange(event.target.value as T)
  }

  const getIcon = (type: IconNameProps | 'domain' | 'color') => {
    if (type) {
      if (type === 'domain') {
        return <Language style={{ color: '#0000008a' }} />
      }
      if (type === 'color') {
        return <PaletteOutlined />
      }

      return <Icon name={type} width={20} />
    } else {
      return <></>
    }
  }

  const uuid = id || `select-label-${generateUUID()}`

  const currentColor = items.find((el: ItemsProps) => el.value === value)?.color ?? '#000000'

  const selectRef = useRef<HTMLDivElement>(null)
  const [width, setWidth] = useState(0)

  useEffect(() => {
    if (selectRef.current) {
      setWidth(selectRef.current.getBoundingClientRect().width)
    }
  }, [selectRef])

  const style = {
    '&:hover fieldset': {
      borderColor: 'var(--primary-color) !important',
      color: 'red',
    },
  }

  return (
    <FormControl
      className={classNames(cls.Select, { [cls.truncateText]: truncateText }, className ? [className] : [])}
      onKeyDown={onKeyDown ?? undefined}
      focused={focused}
      error={error}
    >
      {label && <InputLabel id={uuid}>{label}</InputLabel>}
      <SelectMui<T>
        sx={disabled ? {} : style}
        autoFocus={focused}
        onKeyDown={(e) => {
          if (e.code === 'Enter') {
            return
          }
        }}
        ref={selectRef}
        className={cls.selectInput}
        labelId={uuid}
        value={(Array.isArray(value) && multiple ? (value as T) : String(value)) as SelectPropsMui<T>['value']}
        style={{ color: currentColor }}
        label={label}
        onChange={handleChange}
        variant={variant}
        disabled={disabled}
        onOpen={(e) => {
          if (setIsOpen) {
            setIsOpen(true)
          }
          setTimeout(() => {
            const menuRef = document.getElementById('menu-')
            if (menuRef) {
              menuRef.classList.add(theme)
              menuRef.style.background = 'transperent !important'
            }
          }, 0)

          onOpen?.(e)
        }}
        onClose={() => {
          if (onClose) {
            onClose()
          }
          if (setIsOpen) {
            setIsOpen(false)
          }
        }}
        multiple={multiple}
        {...rest}
      >
        {items.map((item) => {
          const color = item?.color ?? '#000000'

          return (
            <MenuItem key={`menu-item-${item.value}`} value={item.value} style={{ color, width }}>
              {item?.icon && <div className={classNames(cls.Icon, {}, [])}>{getIcon(item?.icon)}</div>}
              <div className={classNames(cls.Label, {}, [])}>{item.label}</div>
            </MenuItem>
          )
        })}
      </SelectMui>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  )
}
