import './lib/formatAVRCHMSpreadsheetData.ts'

import { HotTableClass } from '@handsontable/react'
import { ClickAwayListener } from '@mui/material'
import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import { getSpreadsheetStageColorByTitle } from 'pages/CalculationsPage/ui/VaultBody/lib'
import { useEffect, useMemo, useRef } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { PageLoader } from 'shared/ui/PageLoader/PageLoader.tsx'
import { useStore } from 'stores/useStore.ts'
import { useSpreadsheetHelpers } from 'widgets/Spreadsheet/hooks'
import {
  formatGesCalculationNumericValue,
  isCellValueNumeric,
  validateSpreadsheetFormula,
} from 'widgets/Spreadsheet/ui/lib'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'

import cls from './AVRCHMSpreadsheet.module.scss'

export const AVRCHMSpreadsheet = observer(() => {
  const { calculationsPageStore, godModeStore } = useStore()
  const { avrchmStore, actualStage, selectedStage, stages } = calculationsPageStore
  const {
    initAvrchm,
    avrchmSpreadsheet,
    avrchmSpreadsheetDataLoaded,
    handleAfterChange,
    selectSpreadsheetCoords,
    handleClickAway,
    resetStore,
  } = avrchmStore
  const { godMode } = godModeStore
  const hotRef = useRef<HotTableClass | null>(null)
  const mounted = useRef(false)
  const { beforeCancelChange } = useSpreadsheetHelpers()
  const selectedStageTitle = useMemo(
    () => stages.find((stage) => stage.value === selectedStage)?.label,
    [stages, selectedStage],
  )
  const actualStageTitle = useMemo(
    () => stages.find((stage) => stage.value === actualStage?.code)?.label,
    [stages, actualStage],
  )

  useEffect(() => {
    mounted.current = true

    return () => {
      resetStore()
      mounted.current = false
    }
  }, [])

  useEffect(() => {
    void initAvrchm(!mounted.current)
  }, [godMode])

  const setHot = (hot: HotTableClass | null) => {
    hotRef.current = hot
  }

  /**
   * Подкрашиваем жирным первую строку заголовков столбцов
   * @param col - Индекс столбца.
   * @param th - Элемент заголовка столбца.
   * @param headerLevel - Уровень заголовка, начиная с 0, где 0 - верхний заголовок.
   */
  const afterGetColHeader: Handsontable.GridSettings['afterGetColHeader'] = (col, th, headerLevel) => {
    const displayedHeaderText = col === -1 && headerLevel === 1 ? 'Час' : (th.textContent ?? '')
    const headerText = displayedHeaderText.replace(/(\r\n|\n|\r)/gm, '').trim()
    const headerStyle = 'line-height: 18px;'
    const stage = selectedStage === 'ACTUAL' ? actualStageTitle : selectedStageTitle
    const finished = stage !== headerText

    th.className = classNames(
      '',
      {
        acceptedBgColor:
          headerLevel >= 0 && col >= 0 ? avrchmSpreadsheet.nestedHeaders[headerLevel][col]?.accepted : false,
        [cls.bold]: headerLevel === 0,
        [getSpreadsheetStageColorByTitle(headerText, finished)]: headerLevel === 1 && col !== -1,
      },
      [],
    )
    th.innerHTML = `
      <div>
        <span style="${headerStyle}">
          ${displayedHeaderText}
        </span>
      </div>
    `
  }

  const handleAfterSelectionEnd: Handsontable.GridSettings['afterSelectionEnd'] = (row) => {
    // При выделении шапки onClick срабатывает с типом mousedown, хотя процесс выделения шапки может быть еще не окончен
    // по этой причине добавляется дополнительный слушатель, чтобы обработать нажатие, после завершения действия.
    // При нажатии на ячейки такой проблемы не возникает.
    if (row < 0) {
      document.addEventListener(
        'mouseup',
        () => {
          selectSpreadsheetCoords(hotRef.current)
        },
        { once: true },
      )
    } else {
      selectSpreadsheetCoords(hotRef.current)
    }
  }

  const beforeRenderer: Handsontable.GridSettings['beforeRenderer'] = (TD, _, __, ___, value, cellProperties) => {
    const message = validateSpreadsheetFormula(value)
    if (message) {
      TD.classList.add('isNotValid')
      cellProperties.comment = getStyledComment('Некорректная формула')
    }
  }

  const handleBeforePaste: Handsontable.GridSettings['beforePaste'] = (data, coords) => {
    if (coords && coords.length > 0) {
      const processedData = data
        .map((row) =>
          row
            .map((cellValue) => {
              if (isCellValueNumeric(cellValue)) {
                return formatGesCalculationNumericValue(cellValue)
              }

              return undefined
            })
            .filter((cellValue) => cellValue !== undefined),
        )
        .filter((row) => row.length > 0)

      if (processedData.length) {
        // Очищаем оригинальный массив и вставляем обработанные данные
        data.length = 0
        data.push(...processedData)
      } else {
        return false
      }
    }
  }

  if (!avrchmSpreadsheetDataLoaded) {
    return <PageLoader />
  }

  return (
    <ClickAwayListener mouseEvent='onMouseDown' onClickAway={handleClickAway}>
      <SpreadsheetReact
        ref={setHot}
        width='1000px'
        height='434px'
        enableFormulasPlugin
        columns={avrchmSpreadsheet.columns}
        data={avrchmSpreadsheet.data}
        nestedHeaders={avrchmSpreadsheet.nestedHeaders}
        cell={avrchmSpreadsheet.cell}
        rowHeaders={avrchmSpreadsheet.rowHeaders}
        afterGetColHeader={afterGetColHeader}
        afterChange={handleAfterChange}
        afterSelectionEnd={handleAfterSelectionEnd}
        beforePaste={handleBeforePaste}
        beforeRenderer={beforeRenderer}
        beforeChange={beforeCancelChange}
        maxRows={avrchmSpreadsheet.data.length}
      />
    </ClickAwayListener>
  )
})
