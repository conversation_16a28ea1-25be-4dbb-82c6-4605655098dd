import { useTheme } from 'app/providers/ThemeProvider'
import { observer } from 'mobx-react-lite'
import { Suspense, useEffect } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { initSpreadsheetPerformance } from 'shared/lib/spreadsheetPerformance'
import { PageLoader } from 'shared/ui/PageLoader/PageLoader'

import { AppRouter } from './routes'

const reloadPage = () => {
  const lastReloadTime = localStorage.getItem('preloadError:time')
  if (lastReloadTime && new Date().getTime() - Number(lastReloadTime) < 1000 * 30) {
    localStorage.setItem('vite:preloadError', JSON.stringify(new Date().getTime()))

    return
  }
  localStorage.setItem('vite:preloadError', JSON.stringify(new Date().getTime()))
  window.location.reload()
}

export const App = observer(() => {
  const { theme } = useTheme()

  useEffect(() => {
    window.addEventListener('vite:preloadError', reloadPage)

    // Инициализируем менеджер производительности таблиц для предотвращения фризов Sidebar
    const performanceManager = initSpreadsheetPerformance({
      debounceDelay: 200,
      pauseDuringAnimation: true,
      threshold: 10,
    })

    return () => {
      window.removeEventListener('vite:preloadError', reloadPage)
      localStorage.removeItem('preloadError:time')
      // Очищаем менеджер производительности при размонтировании
      performanceManager?.destroy()
    }
  }, [])

  return (
    <div className={classNames('app', {}, [theme])}>
      <Suspense fallback={<PageLoader />}>
        <AppRouter />
      </Suspense>
    </div>
  )
})

export default App
