import { makeAutoObservable } from 'mobx'

export class GodModeStore {
  godMode: boolean = false

  constructor() {
    makeAutoObservable(this)
    localStorage.setItem('godMode', 'false')
  }

  toggleGodMode = () => {
    this.godMode = !this.godMode
    localStorage.setItem('godMode', String(this.godMode))
  }

  resetGodMode = () => {
    this.godMode = false
    localStorage.removeItem('godMode')
  }
}
